<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE fontconfig SYSTEM "../fonts.dtd">
<!-- /etc/fonts/conf.d/57-dejavu-sans.conf

     Define aliasing and other fontconfig settings for
     DejaVu Sans.

     © 2006-2008 Nicolas Mailhot <nicolas.mailhot at laposte.net>
-->
<fontconfig>
  <!-- Font substitution rules -->
  <alias binding="same">
    <family>Arev Sans</family>
    <accept>
      <family>DejaVu Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Bepa</family>
    <accept>
      <family>DejaVu Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Bitstream Prima Sans</family>
    <accept>
      <family>DejaVu Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Bitstream Vera Sans</family>
    <accept>
      <family>DejaVu Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>DejaVu LGC Sans</family>
    <accept>
      <family>DejaVu Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Hunky Sans</family>
    <accept>
      <family>DejaVu Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Olwen Sans</family>
    <accept>
      <family>DejaVu Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>SUSE Sans</family>
    <accept>
      <family>DejaVu Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Verajja</family>
    <accept>
      <family>DejaVu Sans</family>
    </accept>
  </alias>
  <!-- In case VerajjaPDA stops declaring itself as Verajja -->
  <alias binding="same">
    <family>VerajjaPDA</family>
    <accept>
      <family>DejaVu Sans</family>
    </accept>
  </alias>
  <!-- Generic name assignment -->
  <alias>
    <family>DejaVu Sans</family>
    <default>
      <family>sans-serif</family>
    </default>
  </alias>
  <!-- Generic name aliasing -->
  <alias>
    <family>sans-serif</family>
    <prefer>
      <family>DejaVu Sans</family>
    </prefer>
  </alias>
</fontconfig>
