<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE fontconfig SYSTEM "../fonts.dtd">
<!-- /etc/fonts/conf.d/57-dejavu-serif.conf

     Define aliasing and other fontconfig settings for
     DejaVu Serif.

     © 2006-2008 Nicolas <PERSON>hot <nicolas.mailhot at laposte.net>
-->
<fontconfig>
  <!-- Font substitution rules -->
  <alias binding="same">
    <family>Bitstream Prima Serif</family>
    <accept>
      <family>DejaVu Serif</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Bitstream Vera Serif</family>
    <accept>
      <family>DejaVu Serif</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>DejaVu LGC Serif</family>
    <accept>
      <family>DejaVu Serif</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Hunky Serif</family>
    <accept>
      <family>DejaVu Serif</family>
    </accept>
  </alias>
  <alias binding="same">
    <family><PERSON><PERSON><PERSON> Serif</family>
    <accept>
      <family>DejaVu Serif</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>SUSE Serif</family>
    <accept>
      <family>DejaVu Serif</family>
    </accept>
  </alias>
  <!-- In case Verajja Serif stops declaring itself as DejaVu Serif -->
  <alias binding="same">
    <family>Verajja Serif</family>
    <accept>
      <family>DejaVu Serif</family>
    </accept>
  </alias>
  <!-- Generic name assignment -->
  <alias>
    <family>DejaVu Serif</family>
    <default>
      <family>serif</family>
    </default>
  </alias>
  <!-- Generic name aliasing -->
  <alias>
    <family>serif</family>
    <prefer>
      <family>DejaVu Serif</family>
    </prefer>
  </alias>
</fontconfig>
